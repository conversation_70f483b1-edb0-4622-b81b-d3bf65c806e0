<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>whitesky cloud platform</title>
    <link>http://localhost:1313/</link>
    <description>Recent content on whitesky cloud platform</description>
    <generator>Hugo</generator>
    <language>en-us</language>
    <lastBuildDate>Thu, 25 Jan 2024 00:00:00 +0000</lastBuildDate>
    <atom:link href="http://localhost:1313/index.xml" rel="self" type="application/rss+xml" />
    <item>
      <title>ObjectSpaces: Scalable S3-Compatible Storage</title>
      <link>http://localhost:1313/blog/objectspaces-s3-compatible-storage/</link>
      <pubDate>Thu, 25 Jan 2024 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/blog/objectspaces-s3-compatible-storage/</guid>
      <description>Learn how ObjectSpaces provide scalable, S3-compatible object storage with advanced features like versioning and object locking for enterprise workloads.</description>
    </item>
    <item>
      <title>Deep Dive: Understanding CloudSpaces</title>
      <link>http://localhost:1313/blog/cloudspaces-deep-dive/</link>
      <pubDate>Sat, 20 Jan 2024 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/blog/cloudspaces-deep-dive/</guid>
      <description>Explore the technical capabilities of CloudSpaces and how they provide granular control over your virtual infrastructure.</description>
    </item>
    <item>
      <title>Introducing WhiteSky Cloud: The European Answer to Cloud Sovereignty</title>
      <link>http://localhost:1313/blog/introducing-whitesky-cloud/</link>
      <pubDate>Mon, 15 Jan 2024 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/blog/introducing-whitesky-cloud/</guid>
      <description>Discover how WhiteSky Cloud provides a sovereign alternative to US-based hyperscalers, offering complete control over your infrastructure while maintaining cloud convenience.</description>
    </item>
    <item>
      <title>Customers</title>
      <link>http://localhost:1313/references/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/references/</guid>
      <description>&lt;h2 id=&#34;reference-cases&#34;&gt;Reference Cases&lt;/h2&gt;&#xA;&lt;h3 id=&#34;corporate&#34;&gt;Corporate&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;America Movil&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h3 id=&#34;msp&#34;&gt;MSP&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;LCI (Mauritius)&lt;/li&gt;&#xA;&lt;li&gt;CloudCom (Belgium)&lt;/li&gt;&#xA;&lt;li&gt;Varity (Netherlands)&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h3 id=&#34;saas&#34;&gt;SaaS&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;Exalate&lt;/li&gt;&#xA;&lt;/ul&gt;</description>
    </item>
    <item>
      <title>Features</title>
      <link>http://localhost:1313/features/</link>
      <pubDate>Mon, 01 Jan 0001 00:00:00 +0000</pubDate>
      <guid>http://localhost:1313/features/</guid>
      <description>&lt;h2 id=&#34;our-technology-stack&#34;&gt;Our Technology Stack&lt;/h2&gt;&#xA;&lt;p&gt;Built entirely by whitesky — based on Linux and KVM.&lt;/p&gt;&#xA;&lt;h3 id=&#34;cloudspaces&#34;&gt;Cloudspaces&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;Virtual machines&lt;/li&gt;&#xA;&lt;li&gt;Direct NVMe&lt;/li&gt;&#xA;&lt;li&gt;SSL, DNS, ingress, load balancing&lt;/li&gt;&#xA;&lt;li&gt;Backups, anti-affinity, software-defined storage&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h3 id=&#34;objectspaces&#34;&gt;Objectspaces&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;S3-compatible&lt;/li&gt;&#xA;&lt;li&gt;Versioning, object locking&lt;/li&gt;&#xA;&lt;li&gt;360PB scale per deployment&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h3 id=&#34;containerspaces&#34;&gt;Containerspaces&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;Geo-redundant Kubernetes&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h3 id=&#34;billing-engine&#34;&gt;Billing Engine&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;Customer invoicing&lt;/li&gt;&#xA;&lt;li&gt;Reseller federation&lt;/li&gt;&#xA;&lt;li&gt;Internal cost allocation&lt;/li&gt;&#xA;&lt;/ul&gt;&#xA;&lt;h3 id=&#34;portals&#34;&gt;Portals&lt;/h3&gt;&#xA;&lt;ul&gt;&#xA;&lt;li&gt;Multi-tenant&lt;/li&gt;&#xA;&lt;li&gt;Multi-site&lt;/li&gt;&#xA;&lt;li&gt;White-labeled&lt;/li&gt;&#xA;&lt;/ul&gt;</description>
    </item>
  </channel>
</rss>
